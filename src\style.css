@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 优化后的浅色主题 - 使用更舒适的色彩 */
    --background: 210 20% 98%;
    --foreground: 210 15% 15%;
    --card: 210 20% 99%;
    --card-foreground: 210 15% 15%;
    --popover: 210 20% 99%;
    --popover-foreground: 210 15% 15%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 210 20% 94%;
    --secondary-foreground: 210 15% 25%;
    --muted: 210 20% 95%;
    --muted-foreground: 210 10% 45%;
    --accent: 217 91% 95%;
    --accent-foreground: 217 91% 25%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 20% 88%;
    --input: 210 20% 92%;
    --ring: 217 91% 60%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }
}
